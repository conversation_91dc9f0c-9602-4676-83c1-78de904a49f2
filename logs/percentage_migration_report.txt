================================================================================
PERCENTAGE CALCULATION MIGRATION REPORT
================================================================================

📊 SUMMARY:
   Files with issues: 10
   Total issues found: 32

📈 ISSUE BREAKDOWN:
   coingecko_extraction: 18 occurrences
   manual_calculation: 14 occurrences

📁 DETAILED BREAKDOWN:

🔍 handlers/discord/market/watchlist_commands.py
---------------------------------------------
   Line 47: manual_calculation
      Code: change_pct = ((price - old_price) / old_price) * 100
      Match: ((price - old_price) / old_price) * 100

   Line 244: manual_calculation
      Code: change_pct = ((price - old_price) / old_price) * 100
      Match: ((price - old_price) / old_price) * 100

🔍 handlers/discord/market/price_alert_commands.py
-----------------------------------------------
   Line 90: manual_calculation
      Code: price_diff = ((price - current_price) / current_price) * 100
      Match: ((price - current_price) / current_price) * 100

   Line 255: manual_calculation
      Code: diff_pct = ((target - current_price) / current_price) * 100
      Match: ((target - current_price) / current_price) * 100

🔍 handlers/discord/market/market_commands.py
------------------------------------------
   Line 424: coingecko_extraction
      Code: change_24h = coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

   Line 481: coingecko_extraction
      Code: gainers = [coin for coin in top_coins if coin.get('price_change_percentage_24h', 0) > 0]
      Match: price_change_percentage_24h

   Line 482: coingecko_extraction
      Code: losers = [coin for coin in top_coins if coin.get('price_change_percentage_24h', 0) < 0]
      Match: price_change_percentage_24h

   Line 484: coingecko_extraction
      Code: gainers = sorted(gainers, key=lambda x: x.get('price_change_percentage_24h', 0), reverse=True)[:8]
      Match: price_change_percentage_24h

   Line 485: coingecko_extraction
      Code: losers = sorted(losers, key=lambda x: x.get('price_change_percentage_24h', 0))[:8]
      Match: price_change_percentage_24h

   Line 492: coingecko_extraction
      Code: change = coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

   Line 514: coingecko_extraction
      Code: change = coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

🔍 handlers/discord/alerts/market_monitor_alerts.py
------------------------------------------------
   Line 83: manual_calculation
      Code: increase = ((current_rate - previous_rate) / previous_rate) * 100
      Match: ((current_rate - previous_rate) / previous_rate) * 100

   Line 125: manual_calculation
      Code: increase = ((current_rate - previous_rate) / previous_rate) * 100
      Match: ((current_rate - previous_rate) / previous_rate) * 100

🔍 services/market/price_alert_service.py
--------------------------------------
   Line 156: manual_calculation
      Code: "percentage_change": ((current_price - previous_price) / previous_price) * 100
      Match: ((current_price - previous_price) / previous_price) * 100

🔍 services/market/market_monitor_service.py
-----------------------------------------
   Line 155: manual_calculation
      Code: spread = ((buy_price - sell_price) / sell_price) * 100
      Match: ((buy_price - sell_price) / sell_price) * 100

   Line 356: manual_calculation
      Code: buy_increase_pct = ((current_buy - self._previous_p2p_buy_rate) / self._previous_p2p_buy_rate) * 100
      Match: ((current_buy - self._previous_p2p_buy_rate) / self._previous_p2p_buy_rate) * 100

   Line 384: manual_calculation
      Code: increase = ((current_rate - previous_rate) / previous_rate) * 100
      Match: ((current_rate - previous_rate) / previous_rate) * 100

   Line 404: manual_calculation
      Code: increase = ((current_rate - previous_rate) / previous_rate) * 100
      Match: ((current_rate - previous_rate) / previous_rate) * 100

🔍 services/market/percentage_calculation_service.py
-------------------------------------------------
   Line 94: manual_calculation
      Code: percentage = ((current_price - base_price) / base_price) * 100
      Match: ((current_price - base_price) / base_price) * 100

   Line 171: coingecko_extraction
      Code: percentage = coin_data.get('price_change_percentage_24h', 0.0) or 0.0
      Match: price_change_percentage_24h

🔍 services/market/market_service.py
---------------------------------
   Line 389: coingecko_extraction
      Code: 'price_change_percentage_24h': coin.get('price_change_percentage_24h', 0),
      Match: price_change_percentage_24h

   Line 389: coingecko_extraction
      Code: 'price_change_percentage_24h': coin.get('price_change_percentage_24h', 0),
      Match: price_change_percentage_24h

   Line 719: coingecko_extraction
      Code: coingecko_data.get('price_change_percentage_24h', 0) or
      Match: price_change_percentage_24h

   Line 844: coingecko_extraction
      Code: 'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
      Match: price_change_percentage_24h

   Line 844: coingecko_extraction
      Code: 'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
      Match: price_change_percentage_24h

   Line 916: coingecko_extraction
      Code: 'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
      Match: price_change_percentage_24h

   Line 916: coingecko_extraction
      Code: 'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
      Match: price_change_percentage_24h

🔍 services/market/trading_time_service.py
---------------------------------------
   Line 271: coingecko_extraction
      Code: 'price_change_24h': coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

   Line 318: manual_calculation
      Code: volume_increase_pct = ((current_volume - avg_previous_volume) / avg_previous_volume) * 100
      Match: ((current_volume - avg_previous_volume) / avg_previous_volume) * 100

   Line 331: coingecko_extraction
      Code: 'price_change_24h': coin.get('price_change_percentage_24h', 0),
      Match: price_change_percentage_24h

   Line 362: coingecko_extraction
      Code: price_change_24h = coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

🔍 utils/ui_components.py
----------------------
   Line 229: manual_calculation
      Code: change_percent = ((price - daily_open) / daily_open) * 100
      Match: ((price - daily_open) / daily_open) * 100

🎯 MIGRATION RECOMMENDATIONS:

1. Replace all ticker.get('percentage') calls with:
   percentage_service.extract_binance_percentage(ticker)

2. Replace all price_change_percentage_24h extractions with:
   percentage_service.extract_coingecko_percentage(coin_data)

3. Replace manual calculations with:
   percentage_service.calculate_percentage_change(current, base)

4. Update daily_open calculations to use rolling_24h method
